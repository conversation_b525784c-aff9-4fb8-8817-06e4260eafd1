package cn.iocoder.yudao.module.bpm.service.task.listener.example;

import cn.iocoder.yudao.module.bpm.service.task.listener.BpmGenericFormChangeStatusListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 客户信息变更监听器示例
 * 
 * 这是一个使用 BpmGenericFormChangeStatusListener 的具体实现示例
 * 用于监听客户信息变更流程的审批通过事件
 * 
 * 使用方式：
 * 1. 这个监听器会自动监听指定流程定义Key的流程实例状态变更事件
 * 2. 当流程审批通过并结束时，会自动触发处理逻辑
 * 3. 不需要在流程定义中配置，它是Spring事件监听器
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class CustomerInfoChangeListener extends BpmGenericFormChangeStatusListener {

    /**
     * 指定要监听的流程定义Key
     * 替换为你实际的流程定义Key
     */
    private static final String PROCESS_DEFINITION_KEY = "customer_info_change_process";

    @Override
    protected String getProcessDefinitionKey() {
        return PROCESS_DEFINITION_KEY;
    }

    @Override
    protected ListenerConfig getListenerConfig() {
        ListenerConfig config = new ListenerConfig();
        
        // 关联字段：用于查找台账流程实例的字段
        config.setRelationField("unifiedSocialCreditCode");
        
        // 变更记录字段：在台账中存储变更记录的字段名
        config.setChangeRecordField("basicInfoChangeRecord");
        
        // 字段映射：源字段 -> 目标字段
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("applyUser", "applyUser");                                    // 申请人 -> 申请人
        fieldMapping.put("applyDate", "changeTime");                                   // 申请日期 -> 变更时间
        fieldMapping.put("customerInvoicingInformationAttachment", "relatedDocuments"); // 附件 -> 相关文档
        config.setFieldMapping(fieldMapping);
        
        // 跳过的字段：这些字段不会被更新到台账中
        config.setSkipFields(Arrays.asList(
            "applyUser",
            "applyDept", 
            "applyDate",
            "PROCESS_STATUS",
            "PROCESS_START_USER_ID"
        ));
        
        // 是否仅记录模式：false表示会更新字段，true表示只添加变更记录
        config.setRecordOnly(false);
        
        return config;
    }
}
