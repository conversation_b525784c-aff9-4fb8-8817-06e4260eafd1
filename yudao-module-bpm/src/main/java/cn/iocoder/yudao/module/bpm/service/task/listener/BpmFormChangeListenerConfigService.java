package cn.iocoder.yudao.module.bpm.service.task.listener;

import cn.iocoder.yudao.module.bpm.service.task.listener.BpmDynamicFormChangeStatusListener.ListenerConfig;

/**
 * BPM 表单变更监听器配置服务接口
 * 
 * 用于动态获取监听器配置，支持多种配置来源：
 * 1. 配置文件（application.yml）
 * 2. 数据库配置
 * 3. 远程配置中心
 * 
 * <AUTHOR>
 */
public interface BpmFormChangeListenerConfigService {

    /**
     * 根据流程定义Key获取监听器配置
     * 
     * @param processDefinitionKey 流程定义Key
     * @return 监听器配置，如果未配置则返回null
     */
    ListenerConfig getListenerConfig(String processDefinitionKey);

    /**
     * 刷新配置缓存
     */
    void refreshConfig();
}
