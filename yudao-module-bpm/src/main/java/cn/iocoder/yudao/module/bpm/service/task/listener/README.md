# BPM 通用表单变更监听器使用指南

## 问题说明

你原来的 `BpmGenericFormChangeStatusListener` 是一个 **Spring 事件监听器**，但你在流程定义中配置的是 Flowable 的 **ExecutionListener**。这两种监听器的工作机制不同：

- **Spring 事件监听器**：监听应用程序内部的事件，自动触发，不需要在流程定义中配置
- **Flowable ExecutionListener**：在流程定义中配置，在特定的流程事件（如开始、结束）时触发

## 解决方案

现在提供了两种解决方案：

### 方案一：使用 Spring 事件监听器（推荐）

#### 特点
- 自动监听所有匹配流程定义Key的流程实例状态变更事件
- 只在流程审批通过并结束时触发
- 不需要在流程定义中配置
- 更加可靠，因为是在流程状态最终确定后触发

#### 使用步骤

1. **创建具体的监听器实现类**

```java
@Component
@Slf4j
public class YourCustomChangeListener extends BpmGenericFormChangeStatusListener {

    @Override
    protected String getProcessDefinitionKey() {
        return "your_process_definition_key"; // 替换为你的流程定义Key
    }

    @Override
    protected ListenerConfig getListenerConfig() {
        ListenerConfig config = new ListenerConfig();
        
        // 关联字段：用于查找台账流程实例的字段
        config.setRelationField("unifiedSocialCreditCode");
        
        // 变更记录字段：在台账中存储变更记录的字段名
        config.setChangeRecordField("basicInfoChangeRecord");
        
        // 字段映射：源字段 -> 目标字段
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("applyUser", "applyUser");
        fieldMapping.put("applyDate", "changeTime");
        fieldMapping.put("customerInvoicingInformationAttachment", "relatedDocuments");
        config.setFieldMapping(fieldMapping);
        
        // 跳过的字段：这些字段不会被更新到台账中
        config.setSkipFields(Arrays.asList(
            "applyUser",
            "applyDept", 
            "applyDate",
            "PROCESS_STATUS",
            "PROCESS_START_USER_ID"
        ));
        
        // 是否仅记录模式：false表示会更新字段，true表示只添加变更记录
        config.setRecordOnly(false);
        
        return config;
    }
}
```

2. **无需在流程定义中配置任何监听器**

监听器会自动工作，当指定的流程审批通过并结束时，会自动触发处理逻辑。

### 方案二：使用 Flowable ExecutionListener

#### 特点
- 在流程定义中配置
- 可以精确控制在哪个节点触发
- 需要通过代理表达式配置

#### 使用步骤

1. **在流程定义中配置 ExecutionListener**

在流程设计器中，为流程或特定节点添加监听器：

- **监听器类型**：`delegateExpression`
- **监听器表达式**：`${bpmGenericFormChangeListener}`
- **监听事件**：`end`（流程结束时触发）
- **扩展字段配置**：
  - 字段名：`listenerConfig`
  - 字段值：JSON配置字符串

2. **JSON配置示例**

```json
{
  "relationField": "unifiedSocialCreditCode",
  "changeRecordField": "basicInfoChangeRecord",
  "fieldMapping": {
    "applyUser": "applyUser",
    "applyDate": "changeTime",
    "customerInvoicingInformationAttachment": "relatedDocuments"
  },
  "skipFields": [
    "applyUser",
    "applyDept",
    "applyDate",
    "PROCESS_STATUS",
    "PROCESS_START_USER_ID"
  ],
  "recordOnly": false
}
```

## 配置参数说明

### ListenerConfig 参数

- **relationField**: 关联字段名，用于查找台账流程实例
- **changeRecordField**: 变更记录字段名，在台账中存储变更记录的字段
- **fieldMapping**: 字段映射关系，key为源字段，value为目标字段
- **skipFields**: 跳过的字段列表，这些字段不会被更新到台账中
- **recordOnly**: 是否仅记录模式
  - `false`: 完整更新模式，会更新字段并添加变更记录
  - `true`: 仅记录模式，只添加变更记录，不更新其他字段

## 推荐使用方案一

**建议使用方案一（Spring 事件监听器）**，原因：

1. **更可靠**：在流程状态最终确定后触发，确保只有审批通过的流程才会执行
2. **更简单**：不需要在流程定义中配置，减少配置错误的可能性
3. **更灵活**：可以轻松地为不同的流程创建不同的监听器实现
4. **更易维护**：代码集中管理，不依赖流程定义配置

## 示例

参考 `CustomerInfoChangeListener` 类，这是一个完整的使用示例。

## 注意事项

1. **流程定义Key**：确保在监听器中配置的流程定义Key与实际流程定义Key一致
2. **关联字段**：确保关联字段在表单数据中存在，且能唯一标识台账流程实例
3. **字段映射**：确保映射的源字段在表单数据中存在
4. **数据库权限**：监听器需要访问 Flowable 的历史表，确保有相应的数据库权限
