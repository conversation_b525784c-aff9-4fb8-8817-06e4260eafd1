# BPM 通用表单变更监听器使用指南

## 问题说明

你原来的 `BpmGenericFormChangeStatusListener` 是一个 **Spring 事件监听器**，但你在流程定义中配置的是 Flowable 的 **ExecutionListener**。这两种监听器的工作机制不同：

- **Spring 事件监听器**：监听应用程序内部的事件，自动触发，不需要在流程定义中配置
- **Flowable ExecutionListener**：在流程定义中配置，在特定的流程事件（如开始、结束）时触发

## 解决方案

现在提供了两种解决方案：

### 方案一：使用 Spring 事件监听器（推荐）

#### 特点
- 自动监听所有匹配流程定义Key的流程实例状态变更事件
- 只在流程审批通过并结束时触发
- 不需要在流程定义中配置
- 更加可靠，因为是在流程状态最终确定后触发

#### 使用步骤

1. **创建具体的监听器实现类**

```java
@Component
@Slf4j
public class YourCustomChangeListener extends BpmGenericFormChangeStatusListener {

    @Override
    protected String getProcessDefinitionKey() {
        return "your_process_definition_key"; // 替换为你的流程定义Key
    }

    @Override
    protected ListenerConfig getListenerConfig() {
        ListenerConfig config = new ListenerConfig();
        
        // 关联字段：用于查找台账流程实例的字段
        config.setRelationField("unifiedSocialCreditCode");
        
        // 变更记录字段：在台账中存储变更记录的字段名
        config.setChangeRecordField("basicInfoChangeRecord");
        
        // 字段映射：源字段 -> 目标字段
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("applyUser", "applyUser");
        fieldMapping.put("applyDate", "changeTime");
        fieldMapping.put("customerInvoicingInformationAttachment", "relatedDocuments");
        config.setFieldMapping(fieldMapping);
        
        // 跳过的字段：这些字段不会被更新到台账中
        config.setSkipFields(Arrays.asList(
            "applyUser",
            "applyDept", 
            "applyDate",
            "PROCESS_STATUS",
            "PROCESS_START_USER_ID"
        ));
        
        // 是否仅记录模式：false表示会更新字段，true表示只添加变更记录
        config.setRecordOnly(false);
        
        return config;
    }
}
```

2. **无需在流程定义中配置任何监听器**

监听器会自动工作，当指定的流程审批通过并结束时，会自动触发处理逻辑。

### 方案二：使用 Flowable ExecutionListener（推荐）

#### 特点
- ✅ 在流程定义中配置，只有配置了的流程才会执行
- ✅ 可以精确控制在哪个节点触发
- ✅ 支持动态配置，不同流程可以有不同的配置
- ✅ 通过代理表达式配置，灵活性高

#### 使用步骤

1. **在流程定义中配置 ExecutionListener**

在流程设计器中，为流程结束事件添加监听器：

- **监听器类型**：`delegateExpression`
- **监听器表达式**：`${bpmGenericFormChangeListener}`
- **监听事件**：`end`（流程结束时触发）

2. **配置监听器参数**

有两种方式配置参数：

**方式A：通过流程变量配置（推荐）**

在流程启动时或流程中设置流程变量 `listenerConfig`，值为JSON配置字符串：

```json
{
  "relationField": "unifiedSocialCreditCode",
  "changeRecordField": "basicInfoChangeRecord",
  "fieldMapping": {
    "applyUser": "applyUser",
    "applyDate": "changeTime",
    "customerInvoicingInformationAttachment": "relatedDocuments"
  },
  "skipFields": [
    "applyUser",
    "applyDept",
    "applyDate",
    "PROCESS_STATUS",
    "PROCESS_START_USER_ID"
  ],
  "recordOnly": false
}
```

**方式B：通过扩展属性配置**

在流程设计器中，为监听器添加扩展属性：
- 字段名：`listenerConfig`
- 字段值：JSON配置字符串（同上）

3. **配置示例代码**

如果你需要在代码中动态设置配置，可以这样做：

```java
// 在流程启动时设置监听器配置
Map<String, Object> variables = new HashMap<>();

// 构建监听器配置
Map<String, Object> listenerConfig = new HashMap<>();
listenerConfig.put("relationField", "unifiedSocialCreditCode");
listenerConfig.put("changeRecordField", "basicInfoChangeRecord");

Map<String, String> fieldMapping = new HashMap<>();
fieldMapping.put("applyUser", "applyUser");
fieldMapping.put("applyDate", "changeTime");
fieldMapping.put("customerInvoicingInformationAttachment", "relatedDocuments");
listenerConfig.put("fieldMapping", fieldMapping);

List<String> skipFields = Arrays.asList(
    "applyUser", "applyDept", "applyDate",
    "PROCESS_STATUS", "PROCESS_START_USER_ID"
);
listenerConfig.put("skipFields", skipFields);
listenerConfig.put("recordOnly", false);

// 将配置转换为JSON字符串并设置为流程变量
String configJson = JsonUtils.toJsonString(listenerConfig);
variables.put("listenerConfig", configJson);

// 启动流程
runtimeService.startProcessInstanceByKey("your_process_key", variables);
```

## 配置参数说明

### ListenerConfig 参数

- **relationField**: 关联字段名，用于查找台账流程实例
- **changeRecordField**: 变更记录字段名，在台账中存储变更记录的字段
- **fieldMapping**: 字段映射关系，key为源字段，value为目标字段
- **skipFields**: 跳过的字段列表，这些字段不会被更新到台账中
- **recordOnly**: 是否仅记录模式
  - `false`: 完整更新模式，会更新字段并添加变更记录
  - `true`: 仅记录模式，只添加变更记录，不更新其他字段

## 推荐使用方案二

**建议使用方案二（Flowable ExecutionListener）**，原因：

1. **按需执行**：只有在流程定义中配置了监听器的流程才会执行，符合你的需求
2. **动态配置**：不需要硬编码流程定义Key，可以为不同流程配置不同参数
3. **精确控制**：可以精确控制在哪个节点触发
4. **灵活性高**：通过JSON配置，可以灵活调整监听器行为

## 示例

参考 `CustomerInfoChangeListener` 类，这是一个完整的使用示例。

## 注意事项

1. **流程定义Key**：确保在监听器中配置的流程定义Key与实际流程定义Key一致
2. **关联字段**：确保关联字段在表单数据中存在，且能唯一标识台账流程实例
3. **字段映射**：确保映射的源字段在表单数据中存在
4. **数据库权限**：监听器需要访问 Flowable 的历史表，确保有相应的数据库权限
