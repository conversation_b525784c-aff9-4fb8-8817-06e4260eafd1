package cn.iocoder.yudao.module.bpm.service.task.listener.example;

import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * BpmGenericFormChangeListener 使用示例
 * 
 * 展示如何在代码中构建监听器配置
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class BpmGenericFormChangeListenerExample {

    /**
     * 构建客户信息变更监听器配置
     * 
     * 这个配置可以在流程启动时设置为流程变量 listenerConfig
     * 或者直接在流程定义的监听器扩展属性中配置
     */
    public String buildCustomerInfoChangeConfig() {
        Map<String, Object> config = new HashMap<>();
        
        // 关联字段：用于查找台账流程实例的字段
        config.put("relationField", "unifiedSocialCreditCode");
        
        // 变更记录字段：在台账中存储变更记录的字段名
        config.put("changeRecordField", "basicInfoChangeRecord");
        
        // 字段映射：源字段 -> 目标字段
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("applyUser", "applyUser");                                    // 申请人 -> 申请人
        fieldMapping.put("applyDate", "changeTime");                                   // 申请日期 -> 变更时间
        fieldMapping.put("customerInvoicingInformationAttachment", "relatedDocuments"); // 附件 -> 相关文档
        fieldMapping.put("companyName", "companyName");                                // 公司名称 -> 公司名称
        fieldMapping.put("contactPhone", "contactPhone");                              // 联系电话 -> 联系电话
        config.put("fieldMapping", fieldMapping);
        
        // 跳过的字段：这些字段不会被更新到台账中
        List<String> skipFields = Arrays.asList(
            "applyUser",
            "applyDept", 
            "applyDate",
            "PROCESS_STATUS",
            "PROCESS_START_USER_ID"
        );
        config.put("skipFields", skipFields);
        
        // 是否仅记录模式：false表示会更新字段，true表示只添加变更记录
        config.put("recordOnly", false);
        
        // 转换为JSON字符串
        String configJson = JsonUtils.toJsonString(config);
        log.info("客户信息变更监听器配置: {}", configJson);
        
        return configJson;
    }

    /**
     * 构建供应商信息变更监听器配置
     */
    public String buildSupplierInfoChangeConfig() {
        Map<String, Object> config = new HashMap<>();
        
        config.put("relationField", "supplierCode");
        config.put("changeRecordField", "supplierChangeRecord");
        
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("supplierName", "supplierName");
        fieldMapping.put("contactPerson", "contactPerson");
        fieldMapping.put("contactPhone", "contactPhone");
        fieldMapping.put("address", "address");
        config.put("fieldMapping", fieldMapping);
        
        List<String> skipFields = Arrays.asList(
            "applyUser",
            "applyDept",
            "PROCESS_STATUS"
        );
        config.put("skipFields", skipFields);
        
        config.put("recordOnly", false);
        
        return JsonUtils.toJsonString(config);
    }

    /**
     * 构建仅记录模式的监听器配置
     * 这种模式只添加变更记录，不更新其他字段
     */
    public String buildRecordOnlyConfig() {
        Map<String, Object> config = new HashMap<>();
        
        config.put("relationField", "businessLicenseNumber");
        config.put("changeRecordField", "auditRecord");
        
        // 仅记录模式下，字段映射用于构建变更记录
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("auditResult", "auditResult");
        fieldMapping.put("auditComment", "auditComment");
        fieldMapping.put("auditor", "auditor");
        config.put("fieldMapping", fieldMapping);
        
        // 仅记录模式
        config.put("recordOnly", true);
        
        return JsonUtils.toJsonString(config);
    }

    /**
     * 演示如何在流程启动时设置监听器配置
     */
    public Map<String, Object> buildProcessVariablesWithListenerConfig() {
        Map<String, Object> variables = new HashMap<>();
        
        // 设置业务数据
        variables.put("unifiedSocialCreditCode", "91110000123456789X");
        variables.put("companyName", "测试公司");
        variables.put("applyUser", "张三");
        variables.put("applyDate", "2024-01-01");
        variables.put("contactPhone", "13800138000");
        
        // 设置监听器配置
        String listenerConfig = buildCustomerInfoChangeConfig();
        variables.put("listenerConfig", listenerConfig);
        
        log.info("流程变量（包含监听器配置）: {}", variables);
        
        return variables;
    }

    /**
     * 打印配置示例
     */
    public void printConfigExamples() {
        log.info("=== BpmGenericFormChangeListener 配置示例 ===");
        
        log.info("1. 客户信息变更配置:");
        log.info(buildCustomerInfoChangeConfig());
        
        log.info("2. 供应商信息变更配置:");
        log.info(buildSupplierInfoChangeConfig());
        
        log.info("3. 仅记录模式配置:");
        log.info(buildRecordOnlyConfig());
        
        log.info("=== 配置示例结束 ===");
    }
}
