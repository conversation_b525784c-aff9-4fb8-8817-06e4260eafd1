package cn.iocoder.yudao.module.bpm.service.task.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.bpm.framework.flowable.core.util.FlowableUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.ExtensionElement;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.impl.el.FixedValue;
import org.springframework.context.annotation.Scope;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 通用表单变更监听器（ExecutionListener版本）
 * 用于在流程定义中通过代理表达式配置的监听器
 * 
 * 使用方式：
 * 1. 在流程定义中配置ExecutionListener
 * 2. 监听器类型：delegateExpression
 * 3. 监听器表达式：${bpmGenericFormChangeListener}
 * 4. 监听事件：end（流程结束时触发）
 * 5. 扩展字段配置：
 *    字段名：listenerConfig
 *    字段值：JSON配置字符串
 * 
 * 配置示例：
 * {
 *   "relationField": "unifiedSocialCreditCode",
 *   "changeRecordField": "basicInfoChangeRecord",
 *   "fieldMapping": {
 *     "applyUser": "applyUser",
 *     "applyDate": "changeTime",
 *     "customerInvoicingInformationAttachment": "relatedDocuments"
 *   },
 *   "skipFields": [
 *     "applyUser",
 *     "applyDept", 
 *     "applyDate",
 *     "PROCESS_STATUS",
 *     "PROCESS_START_USER_ID"
 *   ]
 * }
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
@Scope("prototype")
public class BpmGenericFormChangeListener implements ExecutionListener {

    public static final String DELEGATE_EXPRESSION = "${bpmGenericFormChangeListener}";

    @Resource
    private HistoryService historyService;

    @Resource
    private RuntimeService runtimeService;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Setter
    private FixedValue listenerConfig;

    /**
     * 监听器配置参数
     */
    @Setter
    @Getter
    public static class ListenerConfig {
        private String relationField;
        private String changeRecordField; // 变更记录字段名，如 "basicInfoChangeRecord"
        private Map<String, String> fieldMapping; // 字段映射关系，如 {"a": "b"}
        private List<String> skipFields; // 跳过的字段列表
        private Boolean recordOnly; // 仅记录模式，只添加变更记录，不更新其他字段

        @Override
        public String toString() {
            return "ListenerConfig{" +
                    "relationField='" + relationField + '\'' +
                    ", changeRecordField='" + changeRecordField + '\'' +
                    ", fieldMapping=" + fieldMapping +
                    ", skipFields=" + skipFields +
                    ", recordOnly=" + recordOnly +
                    '}';
        }
    }

    @Override
    public void notify(DelegateExecution execution) {
        try {
            log.info("=== BpmGenericFormChangeListener.notify() 被调用 ===");
            log.info("流程实例ID: {}", execution.getProcessInstanceId());

            // 1. 解析监听器配置
            ListenerConfig config = parseListenerConfig(execution);
            if (config == null) {
                log.warn("监听器配置为空，跳过处理");
                return;
            }
            log.info("监听器配置: {}", config);

            // 2. 检查流程是否审批通过
            if (!isProcessApproved(execution)) {
                log.debug("流程未审批通过，跳过处理");
                return;
            }

            // 3. 获取流程实例
            HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(execution.getProcessInstanceId())
                    .includeProcessVariables()
                    .singleResult();

            if (processInstance == null) {
                log.warn("流程实例不存在: {}", execution.getProcessInstanceId());
                return;
            }

            // 4. 获取表单数据
            log.info("获取表单数据...");
            Map<String, Object> formVariables = FlowableUtils.getProcessInstanceFormVariable(processInstance);
            log.info("表单数据: {}", formVariables);
            if (CollUtil.isEmpty(formVariables)) {
                log.warn("流程实例 {} 没有表单数据", execution.getProcessInstanceId());
                return;
            }

            // 5. 更新台账流程实例
            log.info("开始更新台账流程实例...");
            updateLedgerProcessInstance(config, formVariables, processInstance);

            log.info("表单变更监听器处理完成，流程实例: {}, 关联字段: {}",
                    execution.getProcessInstanceId(), config.getRelationField());

        } catch (Exception e) {
            log.error("表单变更监听器处理失败，流程实例: {}", execution.getProcessInstanceId(), e);
        }
    }

    /**
     * 解析监听器配置
     * 支持多种配置方式：
     * 1. 从扩展字段中获取 JSON 配置（推荐方式）
     * 2. 从流程变量中获取配置
     * 3. 从注入的 listenerConfig 字段获取配置（兼容旧版本）
     */
    private ListenerConfig parseListenerConfig(DelegateExecution execution) {
        try {
            String configJson = null;

            // 方式1: 从扩展字段中获取配置（推荐方式）
            configJson = getConfigFromExtensionElements(execution);

            // 方式2: 从流程变量中获取配置
            if (StrUtil.isBlank(configJson)) {
                configJson = (String) execution.getVariable("listenerConfig");
                if (StrUtil.isNotBlank(configJson)) {
                    log.info("从流程变量获取监听器配置");
                }
            }

            // 方式3: 从注入的字段获取配置（兼容旧版本）
            if (StrUtil.isBlank(configJson) && listenerConfig != null) {
                configJson = listenerConfig.getExpressionText();
                if (StrUtil.isNotBlank(configJson)) {
                    log.info("从注入字段获取监听器配置");
                }
            }

            if (StrUtil.isBlank(configJson)) {
                log.warn("未找到监听器配置，请检查以下配置：");
                log.warn("1. 流程定义中的扩展字段 listenerConfig");
                log.warn("2. 流程变量 listenerConfig");
                log.warn("3. 注入的 listenerConfig 字段");
                return null;
            }

            log.info("监听器配置JSON: {}", configJson);

            // 解析JSON配置
            ListenerConfig config = JsonUtils.parseObject(configJson, ListenerConfig.class);
            if (config == null) {
                log.warn("监听器配置解析失败，JSON格式可能有误: {}", configJson);
                return null;
            }

            // 验证必要字段
            if (StrUtil.isBlank(config.getRelationField())) {
                log.warn("监听器配置缺少必要字段 relationField");
                return null;
            }

            log.info("解析的监听器配置: {}", config);
            return config;

        } catch (Exception e) {
            log.error("解析监听器配置失败", e);
            return null;
        }
    }

    /**
     * 从扩展元素中获取配置
     * 这是推荐的配置方式，在流程设计器中配置扩展字段
     *
     * 在流程设计器中配置方式：
     * 1. 选择流程结束事件
     * 2. 添加执行监听器
     * 3. 监听器类型：delegateExpression
     * 4. 监听器表达式：${bpmGenericFormChangeListener}
     * 5. 事件：end
     * 6. 在扩展属性中添加字段：
     *    - 字段名：listenerConfig
     *    - 字段值：JSON配置字符串
     */
    private String getConfigFromExtensionElements(DelegateExecution execution) {
        try {
            // 方式1: 从流程变量中获取（最常用的方式）
            Object configObj = execution.getVariable("listenerConfig");
            if (configObj != null) {
                String configJson = configObj.toString();
                if (StrUtil.isNotBlank(configJson)) {
                    log.info("从流程变量获取监听器配置");
                    return configJson.trim();
                }
            }

            // 方式2: 从当前流程元素的扩展元素中获取
            if (execution.getCurrentFlowElement() != null) {
                Map<String, List<ExtensionElement>> extensionElements =
                    execution.getCurrentFlowElement().getExtensionElements();

                if (extensionElements != null) {
                    List<ExtensionElement> configElements = extensionElements.get("listenerConfig");
                    if (CollUtil.isNotEmpty(configElements)) {
                        String configJson = configElements.get(0).getElementText();
                        if (StrUtil.isNotBlank(configJson)) {
                            log.info("从流程元素扩展元素获取监听器配置");
                            return configJson.trim();
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.debug("从扩展元素获取配置失败，将尝试其他方式: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 检查流程是否审批通过
     */
    private boolean isProcessApproved(DelegateExecution execution) {
        // 通过流程变量判断是否审批通过
        Object statusVar = execution.getVariable("status");
        if (statusVar == null) {
            log.debug("流程状态变量为空，默认认为审批通过");
            return true; // 如果没有状态变量，默认认为审批通过
        }
        
        // 状态为2表示审批通过
        boolean approved = Integer.valueOf(2).equals(statusVar);
        log.info("流程审批状态: {}, 是否通过: {}", statusVar, approved);
        return approved;
    }

    /**
     * 更新台账流程实例
     */
    private void updateLedgerProcessInstance(ListenerConfig config, Map<String, Object> formVariables, 
                                           HistoricProcessInstance changeProcessInstance) {
        try {
            // 1. 获取关联字段值
            Object relationValue = formVariables.get(config.getRelationField());
            if (relationValue == null) {
                log.warn("关联字段 {} 的值为空，无法查找台账流程实例", config.getRelationField());
                return;
            }

            log.info("关联字段: {}, 关联值: {}", config.getRelationField(), relationValue);

            // 2. 查找台账流程实例
            HistoricProcessInstance ledgerProcessInstance = findLedgerProcessInstance(config.getRelationField(), relationValue);
            if (ledgerProcessInstance == null) {
                log.warn("未找到关联的台账流程实例，关联字段: {}, 关联值: {}", config.getRelationField(), relationValue);
                return;
            }

            log.info("找到台账流程实例: {}, 流程名称: {}", ledgerProcessInstance.getId(), ledgerProcessInstance.getName());

            // 3. 更新台账流程实例的历史变量
            updateLedgerVariables(ledgerProcessInstance.getId(), formVariables, changeProcessInstance, config);

            log.info("成功更新台账流程实例，台账流程ID: {}, 台账流程名称: {}, 关联字段: {}, 关联值: {}",
                    ledgerProcessInstance.getId(), ledgerProcessInstance.getName(), config.getRelationField(), relationValue);

        } catch (Exception e) {
            log.error("更新台账流程实例失败，关联字段: {}", config.getRelationField(), e);
            throw new RuntimeException("更新台账流程实例失败", e);
        }
    }

    /**
     * 查找台账流程实例
     */
    private HistoricProcessInstance findLedgerProcessInstance(String relationField, Object relationValue) {
        try {
            // 查询历史变量表，找到包含指定关联字段值的流程实例
            List<Map<String, Object>> results = jdbcTemplate.queryForList(
                    "SELECT DISTINCT PROC_INST_ID_ FROM ACT_HI_VARINST WHERE NAME_ = ? AND TEXT_ = ?",
                    relationField, relationValue.toString()
            );

            if (CollUtil.isEmpty(results)) {
                return null;
            }

            // 取第一个匹配的流程实例ID
            String processInstanceId = (String) results.get(0).get("PROC_INST_ID_");

            // 获取流程实例详情
            return historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();

        } catch (Exception e) {
            log.error("查找台账流程实例失败，关联字段: {}, 关联值: {}", relationField, relationValue, e);
            return null;
        }
    }

    /**
     * 更新台账流程实例的历史变量
     */
    private void updateLedgerVariables(String ledgerProcessInstanceId, Map<String, Object> formVariables,
                                     HistoricProcessInstance changeProcessInstance, ListenerConfig config) {
        try {
            boolean isRecordOnly = Boolean.TRUE.equals(config.getRecordOnly());
            log.info("更新模式: {}", isRecordOnly ? "仅记录模式" : "完整更新模式");

            if (!isRecordOnly) {
                // 1. 更新表单字段（根据配置跳过指定字段）
                log.info("更新表单字段...");
                for (Map.Entry<String, Object> entry : formVariables.entrySet()) {
                    String fieldName = entry.getKey();

                    // 检查是否在跳过字段列表中
                    if (shouldSkipField(fieldName, config)) {
                        log.debug("跳过配置的字段: {}", fieldName);
                        continue;
                    }

                    // 检查台账中是否已存在该变量
                    if (!isVariableExistsInLedger(ledgerProcessInstanceId, fieldName)) {
                        log.debug("台账中不存在字段 {}，跳过更新", fieldName);
                        continue;
                    }

                    log.info("更新字段: {} = {}", fieldName, entry.getValue());
                    updateHistoricVariable(ledgerProcessInstanceId, fieldName, entry.getValue());
                }

                // 2. 更新特殊字段：申请人、变更时间
                log.info("更新特殊字段...");
                updateHistoricVariable(ledgerProcessInstanceId, "applicant", changeProcessInstance.getStartUserId());
                updateHistoricVariable(ledgerProcessInstanceId, "change_time", LocalDateTime.now());
                updateHistoricVariable(ledgerProcessInstanceId, "update_time", LocalDateTime.now());
            } else {
                log.info("仅记录模式：跳过字段更新，只处理变更记录");
            }

            // 3. 处理变更记录字段（如果配置了的话）
            if (StrUtil.isNotBlank(config.getChangeRecordField())) {
                log.info("处理变更记录字段: {}", config.getChangeRecordField());
                addChangeRecord(ledgerProcessInstanceId, formVariables, changeProcessInstance, config);
            }

        } catch (Exception e) {
            log.error("更新台账历史变量失败，台账流程: {}", ledgerProcessInstanceId, e);
            throw e;
        }
    }

    /**
     * 检查是否应该跳过某个字段
     */
    private boolean shouldSkipField(String fieldName, ListenerConfig config) {
        if (CollUtil.isEmpty(config.getSkipFields())) {
            return false;
        }
        return config.getSkipFields().contains(fieldName);
    }

    /**
     * 检查台账中是否存在指定变量
     */
    private boolean isVariableExistsInLedger(String processInstanceId, String variableName) {
        try {
            Integer count = jdbcTemplate.queryForObject(
                    "SELECT COUNT(*) FROM ACT_HI_VARINST WHERE PROC_INST_ID_ = ? AND NAME_ = ?",
                    Integer.class, processInstanceId, variableName
            );
            return count != null && count > 0;
        } catch (Exception e) {
            log.warn("检查变量是否存在失败: {}", variableName, e);
            return false;
        }
    }

    /**
     * 更新历史变量
     */
    private void updateHistoricVariable(String processInstanceId, String variableName, Object value) {
        try {
            String valueStr = value != null ? value.toString() : null;
            jdbcTemplate.update(
                    "UPDATE ACT_HI_VARINST SET TEXT_ = ?, LAST_UPDATED_TIME_ = ? WHERE PROC_INST_ID_ = ? AND NAME_ = ?",
                    valueStr, new Date(), processInstanceId, variableName
            );
        } catch (Exception e) {
            log.error("更新历史变量失败: {} = {}", variableName, value, e);
        }
    }

    /**
     * 添加变更记录到台账流程实例
     */
    private void addChangeRecord(String ledgerProcessInstanceId, Map<String, Object> formVariables,
                               HistoricProcessInstance changeProcessInstance, ListenerConfig config) {
        try {
            log.info("开始添加变更记录，变更记录字段: {}", config.getChangeRecordField());

            // 1. 获取台账流程实例中现有的变更记录
            List<Map<String, Object>> existingChangeRecords = getExistingChangeRecords(ledgerProcessInstanceId, config.getChangeRecordField());
            log.info("台账中现有的变更记录数量: {}", existingChangeRecords.size());

            // 2. 根据字段映射构建新的变更记录
            Map<String, Object> newChangeRecord = buildChangeRecord(formVariables, changeProcessInstance, config);
            log.info("构建的新变更记录: {}", newChangeRecord);

            if (newChangeRecord.isEmpty()) {
                log.warn("没有构建出有效的变更记录，跳过添加");
                return;
            }

            // 3. 将新记录添加到现有记录列表中
            existingChangeRecords.add(newChangeRecord);

            // 4. 更新台账流程实例的变更记录字段
            updateHistoricVariable(ledgerProcessInstanceId, config.getChangeRecordField(), existingChangeRecords);

            log.info("成功添加变更记录，台账流程: {}, 变更记录字段: {}, 新记录数量: {}",
                    ledgerProcessInstanceId, config.getChangeRecordField(), existingChangeRecords.size());

        } catch (Exception e) {
            log.error("添加变更记录失败，台账流程: {}, 变更记录字段: {}",
                    ledgerProcessInstanceId, config.getChangeRecordField(), e);
            // 不抛出异常，避免影响其他处理
            log.warn("跳过变更记录的添加，继续处理其他逻辑");
        }
    }

    /**
     * 获取现有的变更记录
     */
    private List<Map<String, Object>> getExistingChangeRecords(String processInstanceId, String changeRecordField) {
        try {
            List<Map<String, Object>> result = jdbcTemplate.queryForList(
                    "SELECT TEXT_ FROM ACT_HI_VARINST WHERE PROC_INST_ID_ = ? AND NAME_ = ?",
                    processInstanceId, changeRecordField
            );

            if (CollUtil.isEmpty(result)) {
                log.info("台账流程实例 {} 中不存在变更记录字段 {}", processInstanceId, changeRecordField);
                return new ArrayList<>();
            }

            String existingRecordsJson = (String) result.get(0).get("TEXT_");
            if (StrUtil.isBlank(existingRecordsJson)) {
                return new ArrayList<>();
            }

            // 解析现有的变更记录
            List<?> rawList = JsonUtils.parseArray(existingRecordsJson, Map.class);
            List<Map<String, Object>> existingRecords = new ArrayList<>();
            if (rawList != null) {
                for (Object item : rawList) {
                    if (item instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> map = (Map<String, Object>) item;
                        existingRecords.add(map);
                    }
                }
            }
            return existingRecords;

        } catch (Exception e) {
            log.error("获取现有变更记录失败，台账流程: {}, 变更记录字段: {}", processInstanceId, changeRecordField, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据字段映射构建变更记录
     */
    private Map<String, Object> buildChangeRecord(Map<String, Object> formVariables,
                                                 HistoricProcessInstance changeProcessInstance,
                                                 ListenerConfig config) {
        Map<String, Object> changeRecord = new HashMap<>();

        try {
            // 如果没有配置字段映射，直接返回空记录
            if (CollUtil.isEmpty(config.getFieldMapping())) {
                log.warn("没有配置字段映射，无法构建变更记录");
                return changeRecord;
            }

            // 根据字段映射构建变更记录
            for (Map.Entry<String, String> mapping : config.getFieldMapping().entrySet()) {
                String sourceField = mapping.getKey();   // 源字段（表单字段）
                String targetField = mapping.getValue(); // 目标字段（变更记录字段）

                Object sourceValue = formVariables.get(sourceField);
                if (sourceValue != null) {
                    changeRecord.put(targetField, sourceValue);
                    log.debug("映射字段: {} -> {} = {}", sourceField, targetField, sourceValue);
                }
            }

            // 添加一些默认字段
            changeRecord.put("changeTime", LocalDateTime.now());
            changeRecord.put("processInstanceId", changeProcessInstance.getId());
            changeRecord.put("startUserId", changeProcessInstance.getStartUserId());

            log.info("构建变更记录完成，包含 {} 个字段", changeRecord.size());

        } catch (Exception e) {
            log.error("构建变更记录失败", e);
        }

        return changeRecord;
    }
}
