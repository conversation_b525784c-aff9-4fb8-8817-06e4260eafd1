package cn.iocoder.yudao.module.bpm.service.task.listener.impl;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.module.bpm.service.task.listener.BpmFormChangeListenerConfigService;
import cn.iocoder.yudao.module.bpm.service.task.listener.BpmDynamicFormChangeStatusListener.ListenerConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * BPM 表单变更监听器配置服务实现类
 * 
 * 基于配置文件的实现，支持从 application.yml 中读取配置
 * 
 * 配置示例：
 * bpm:
 *   form-change-listeners:
 *     - processDefinitionKey: "customer_info_change"
 *       relationField: "unifiedSocialCreditCode"
 *       changeRecordField: "basicInfoChangeRecord"
 *       fieldMapping:
 *         applyUser: "applyUser"
 *         applyDate: "changeTime"
 *         customerInvoicingInformationAttachment: "relatedDocuments"
 *       skipFields: ["applyUser", "applyDept", "applyDate", "PROCESS_STATUS", "PROCESS_START_USER_ID"]
 *       recordOnly: false
 *       enabled: true
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
@ConfigurationProperties(prefix = "bpm")
public class BpmFormChangeListenerConfigServiceImpl implements BpmFormChangeListenerConfigService {

    /**
     * 监听器配置列表（从配置文件读取）
     */
    private List<ListenerConfig> formChangeListeners;

    /**
     * 配置缓存（processDefinitionKey -> ListenerConfig）
     */
    private final Map<String, ListenerConfig> configCache = new ConcurrentHashMap<>();

    /**
     * 设置监听器配置列表（Spring Boot 配置绑定）
     */
    public void setFormChangeListeners(List<ListenerConfig> formChangeListeners) {
        this.formChangeListeners = formChangeListeners;
    }

    /**
     * 获取监听器配置列表
     */
    public List<ListenerConfig> getFormChangeListeners() {
        return formChangeListeners;
    }

    @PostConstruct
    public void init() {
        refreshConfig();
    }

    @Override
    public ListenerConfig getListenerConfig(String processDefinitionKey) {
        if (processDefinitionKey == null) {
            return null;
        }
        
        ListenerConfig config = configCache.get(processDefinitionKey);
        if (config != null) {
            log.debug("从缓存获取监听器配置: processDefinitionKey={}, config={}", processDefinitionKey, config);
        } else {
            log.debug("未找到监听器配置: processDefinitionKey={}", processDefinitionKey);
        }
        
        return config;
    }

    @Override
    public void refreshConfig() {
        configCache.clear();
        
        if (CollUtil.isEmpty(formChangeListeners)) {
            log.info("未配置任何表单变更监听器");
            return;
        }

        log.info("开始加载表单变更监听器配置，共 {} 个", formChangeListeners.size());
        
        for (ListenerConfig config : formChangeListeners) {
            if (config.getProcessDefinitionKey() == null) {
                log.warn("监听器配置缺少 processDefinitionKey，跳过: {}", config);
                continue;
            }
            
            // 设置默认值
            if (config.getEnabled() == null) {
                config.setEnabled(true);
            }
            if (config.getRecordOnly() == null) {
                config.setRecordOnly(false);
            }
            
            configCache.put(config.getProcessDefinitionKey(), config);
            log.info("加载监听器配置: processDefinitionKey={}, enabled={}, recordOnly={}, relationField={}, changeRecordField={}",
                    config.getProcessDefinitionKey(), config.getEnabled(), config.getRecordOnly(),
                    config.getRelationField(), config.getChangeRecordField());
        }
        
        log.info("表单变更监听器配置加载完成，共加载 {} 个有效配置", configCache.size());
    }
}
